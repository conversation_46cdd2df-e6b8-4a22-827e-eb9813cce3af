# 控件规范说明

## DocumentData基础结构
```json
{
  "type": "${documentType}",
  "title": "文档标题",
  "subtitle": "副标题（可选）",
  "widgets": [
    // 控件数组
  ]
}
```

## 控件通用规范
- **必需字段**：`type`、`serial`
- **序列编号**：层级结构 "0" → "0.1" → "1" → "1.1" → "1.1.1" → "1.1.1.1"
- **灵活层级**：可根据内容需要省略中间层级

## TITLE控件

**用途**：文档层次结构定义

**样式类型**：
- `DOCUMENT`：文档标题（最高级别）
- `SECTION`：章节标题
- `PARAGRAPH`：段落标题
- `ENTRY`：条目标题

**字段结构**：
```json
{
  "serial": "1.1",
  "type": "TITLE",
  "style": "DOCUMENT|SECTION|PARAGRAPH|ENTRY",
  "title": "主标题（必填）",
  "subtitle": "副标题或补充说明（可选）"
}
```

**样式编号规则**：
- `DOCUMENT`样式：编号固定为"0"（用于文档标题）
- `SECTION`样式：编号应大于0（如"1"、"2"、"3"等，用于章节标题）
- `PARAGRAPH`样式：编号应为二级（如"1.1"、"1.2"、"2.1"等，用于段落标题）
- `ENTRY`样式：编号应为三级（如"1.1.1"、"1.2.1"、"2.1.1"等，用于条目标题）

## TEXT控件

**用途**：文本内容展示

**样式类型**：
- `EMPHASIS`：强调性结论
- `BLOCK`：重要信息块
- `PLAIN`：普通文本
- `FLOAT`：浮层效果文本

**字段结构**：
```json
{
  "serial": "1.2",
  "type": "TEXT",
  "style": "EMPHASIS|PLAIN|BLOCK|FLOAT",
  "title": "标题（可选）",
  "content": "文本内容（必填）"
}
```

**FLOAT样式说明**：
- **用途**：用于呈现具有"浮层"视觉效果的文本内容
- **视觉效果**：文本内容以浮层形式展示，通常具有阴影、边框或背景色等视觉特效
- **必要应用**：第一章节前的摘要性内容（如"引言"、"摘要"、"前言"等）必须使用FLOAT样式呈现
- **其他适用场景**：文档中需要突出强调的重要提示、关键信息、特别说明等位置

**BLOCK样式说明**：
- **用途**：用于展示重要的分析性内容，需要突出显示
- **适用内容**：数据解读、专业分析、市场洞察、趋势判断等
- **视觉效果**：通过背景色或边框突出显示，提升内容重要性

## LIST控件

**用途**：列表内容展示

**样式类型**：
- `SERIAL`：有序列表（带序号）
- `ITEM`：无序列表（项目符号）

**字段结构**：
```json
{
  "serial": "1.2",
  "type": "LIST",
  "style": "SERIAL|ITEM",
  "title": "列表标题",
  "content": [
    {
      "title": "项目标题",
      "content": "项目内容"
    },
    {
      "title": "项目标题",
      "content": "项目内容"
    }
  ]
}
```

**重要约束**：
- content必须为对象数组格式，不能使用字符串数组
- SERIAL样式的title不包含序号

## TABLE控件

**用途**：结构化数据展示

**样式类型**：
- `NORMAL`：普通表格
- `BOARD`：数据面板

**字段结构**：
```json
{
  "serial": "3.1",
  "type": "TABLE",
  "style": "NORMAL|BOARD",
  "title": "表格标题",
  "cols": ["列1", "列2", "列3"],
  "content": [
    [
      {"type": "TEXT", "content": "内容", "recommended": true},
      {"type": "TEXT", "content": "内容"}
    ]
  ]
}
```

**TableCell类型说明**：
- `TEXT`：文本内容（最常用）
- `IMAGE`：图片URL
- `PROGRESS_BAR`：进度值(0-100的数字)
- `CHANGE`：涨跌幅数据

**严格要求**：
- `cols`数组长度必须等于每行单元格数量
- 每个单元格必须包含`type`和`content`字段
- `recommended`字段仅在需要标识推荐选项时使用

## CHART控件

**用途**：数据可视化展示

**样式类型**：
- `PIE`：饼图（适用于占比、分布数据）
- `BAR`：柱状图（适用于对比、分类数据）
- `LINE`：折线图（适用于趋势、时间序列数据）

### PIE图格式
```json
{
  "serial": "2.1",
  "type": "CHART",
  "style": "PIE",
  "title": "数据分布（单位说明）",
  "content": [
    {
      "title": "分类名称",
      "content": 数值
    }
  ]
}
```

### BAR/LINE图格式
```json
{
  "serial": "2.2",
  "type": "CHART",
  "style": "BAR|LINE",
  "title": "数据对比（单位说明）",
  "cols": ["列1", "列2", "列3"],
  "content": [
    {
      "title": "数据系列名称",
      "content": [数值1, 数值2, 数值3]
    }
  ]
}
```

**格式要求**：
- BAR/LINE图必须包含`cols`字段
- `cols`数组长度必须等于`content`中每个数据系列的数值数量
- content中对象必须使用"title"和"content"属性名
- 所有数值必须为数字类型，不能包含文字单位

## HOUSING_CARD控件

**用途**：房源信息展示

**字段结构**：
```json
{
  "serial": "4.1",
  "type": "HOUSING_CARD",
  "name": "房源名称",
  "layout": "户型",
  "area": "面积",
  "floor": "楼层",
  "location": "位置",
  "price": "总价（字符串格式，如'235万'）",
  "unitPrice": "单价（字符串格式，如'50,000元/m²'）",
  "tags": [
    "标签1",
    "标签2"
  ]
}
```

**重要说明**：
- price和unitPrice字段必须为字符串类型，包含单位信息
- 示例：`"price": "235万"`，`"unitPrice": "50,000元/m²"`

## CARD控件

**用途**：结构化信息展示，支持多种卡片类型

**样式类型**：
- `BROKER`：经纪人卡片
- `HOUSING`：房源卡片
- `COMMUNITY`：小区卡片

### BROKER卡片
```json
{
  "serial": "4.2",
  "type": "CARD",
  "style": "BROKER",
  "title": "置业顾问推荐",
  "name": "张经理",
  "subtitle": "资深置业顾问",
  "tags": ["资深", "专业", "高评分"],
  "fields": {
    "phone": "138-0000-1234",
    "experience": "8年",
    "specialty": "静安区房产投资、学区房置业",
    "serviceArea": "大宁板块、彭浦板块",
    "rating": "4.9分（基于156条评价）",
    "achievement": "累计服务客户500+，成交金额超2亿元"
  }
}
```

### HOUSING卡片
```json
{
  "serial": "4.3",
  "type": "CARD",
  "style": "HOUSING",
  "title": "优质房源推荐",
  "name": "慧芝湖花园精装三房",
  "subtitle": "满五唯一学区房",
  "tags": ["满五唯一", "学区房", "地铁房", "精装修"],
  "fields": {
    "layout": "3室2厅2卫",
    "area": "110㎡",
    "floor": "15/18层",
    "orientation": "朝南",
    "decoration": "精装修",
    "totalPrice": "1073万",
    "unitPrice": "97,545元/㎡",
    "propertyType": "住宅"
  }
}
```

### COMMUNITY卡片
```json
{
  "serial": "4.4",
  "type": "CARD",
  "style": "COMMUNITY",
  "title": "社区配套介绍",
  "name": "慧芝湖花园社区",
  "subtitle": "高品质花园式社区",
  "tags": ["高绿化", "低密度", "品牌物业"],
  "fields": {
    "buildYear": "2004-2009年",
    "propertyCompany": "龙湖物业",
    "propertyFee": "2.7元/月/㎡",
    "greenRate": "45%",
    "plotRatio": "2.5",
    "parkingSpaces": "3494个（车位比1:0.7）",
    "facilities": "中央景观带、儿童游乐区、健身设施、24小时安保"
  }
}
```

**重要说明**：
- fields对象包含该卡片类型的特定字段信息
- tags数组最多包含5个标签，优先选择最有价值的特征
- 所有字段值必须为字符串类型，保持原始数据格式
